# ===== BUILD STAGE =====
# Use Bun alpine for fastest builds and better dependency management
FROM oven/bun:1.1.42-alpine AS dependencies

# Set build environment variables for optimal builds
ENV NODE_ENV=production \
    PRISMA_GENERATE_SKIP_DOWNLOAD=true \
    PRISMA_GENERATE_SKIP_AUTOINSTALL=true \
    BUN_INSTALL_CACHE_DIR=/tmp/bun-cache \
    npm_config_cache=/tmp/npm-cache

WORKDIR /app

# Install build dependencies and create users in single optimized layer
RUN apk add --no-cache --virtual .build-deps openssl python3 make g++ \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc \
    && addgroup -g 1001 -S nodejs \
    && adduser -S builduser -u 1001 -G nodejs

# Copy root config files (most stable layer)
COPY tsconfig.json ./tsconfig.json

# ===== SHARED LIBRARY BUILD =====
FROM dependencies AS shared-builder

# Copy shared package.json for dependency caching
COPY shared/package.json ./shared/package.json
WORKDIR /app/shared

# Install dependencies with TypeScript for build
RUN bun install --frozen-lockfile \
    && bun add -D typescript \
    && bun pm cache rm \
    && rm -rf ~/.bun /tmp/* /usr/local/share/.cache

# Copy and build shared library
WORKDIR /app/shared
COPY shared/package.json ./package.json
COPY shared/tsconfig.json ./tsconfig.json
COPY shared/src ./src
RUN bun run build

# Cleanup shared library but keep dist folder
RUN rm -rf node_modules src *.config.* tsconfig.json \
    && find . -name "*.map" -delete \
    && find . -name "*.env*" -type f -delete \
    && find . -name "*.test.*" -type f -delete \
    && find . -name "*.spec.*" -type f -delete \
    && find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true
WORKDIR /app

# ===== SERVER BUILD =====
FROM dependencies AS server-builder

# Copy built shared library from previous stage first
COPY --from=shared-builder /app/shared /app/shared

# Copy server package.json
COPY server/package.json ./server/package.json

# Create minimal workspace package.json for server build
RUN echo '{"name":"sumopod-server-build","workspaces":["./server","./shared"]}' > package.json

# Temporarily remove Prisma client for faster initial install
RUN cd server && sed -i '/"@prisma\/client"/d' package.json \
    && cd .. && bun install --frozen-lockfile \
    && bun pm cache rm \
    && rm -rf ~/.bun/install/cache

WORKDIR /app/server



# Copy server source and schema
COPY server/src ./src
COPY server/prisma ./prisma
COPY server/tsconfig.json ./tsconfig.json

# Install Prisma client and generate schema with aggressive cleanup
RUN bun add @prisma/client \
    && bunx prisma generate \
    && bun pm cache rm \
    && rm -rf ~/.bun /tmp/* /usr/local/share/.cache

# Build server application with aggressive cleanup
RUN bun run build \
    && rm -rf node_modules src *.config.* tsconfig.json \
    && find . -name "*.map" -delete \
    && find . -name "*.env*" -type f -delete \
    && find . -name "*.test.*" -type f -delete \
    && find . -name "*.spec.*" -type f -delete \
    && find . -name "*.md" -type f -delete \
    && find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true \
    && find . -name ".git*" -delete 2>/dev/null || true

# ===== PRODUCTION RUNTIME STAGE =====
# Use Bun alpine for consistent runtime environment
FROM oven/bun:1.1.42-alpine AS production

# Install only essential runtime dependencies with virtual package for easy cleanup
RUN apk add --no-cache --virtual .runtime-deps openssl dumb-init tini \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc /root/.cache \
    && addgroup -g 1001 -S nodejs \
    && adduser -S sumopod -u 1001 -G nodejs

WORKDIR /app

# Copy only essential production files with proper ownership (minimal layers)
COPY --from=server-builder --chown=sumopod:nodejs /app/server/dist ./server/dist
COPY --from=server-builder --chown=sumopod:nodejs /app/server/package.json ./server/package.json
COPY --from=server-builder --chown=sumopod:nodejs /app/server/prisma ./server/prisma
COPY --from=shared-builder --chown=sumopod:nodejs /app/shared/dist ./shared/dist

# ===== PRODUCTION DEPENDENCIES =====
# Install only production runtime dependencies
WORKDIR /app/server

# Replace workspace dependency with relative path for bun compatibility
RUN sed -i 's/"shared": "workspace:\*"/"shared": "file:..\/shared\/dist"/' package.json

# Install production dependencies with bun
RUN bun install --production --frozen-lockfile \
    && bun add @prisma/client \
    && bunx prisma generate \
    && rm -rf ~/.bun/install/cache /tmp/* /root/.cache

# Setup entrypoint and create necessary directories in single layer
COPY --chown=sumopod:nodejs server/docker-entrypoint /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint \
    && mkdir -p /app/logs /app/uploads \
    && chown -R sumopod:nodejs /app/logs /app/uploads \
    && chmod -R 755 /app/logs /app/uploads

# Security: Switch to non-root user
USER sumopod

# Optimized health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD node -e "require('http').get('http://localhost:8080/health', {timeout:3000}, (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Expose port
EXPOSE 8080

# Set working directory
WORKDIR /app/server

# Use tini for proper signal handling and process management
ENTRYPOINT ["tini", "--"]
CMD ["docker-entrypoint", "bun", "run", "start"]
