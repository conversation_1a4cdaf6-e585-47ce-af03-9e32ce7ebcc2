# Server Environment Variables for Easy Panel Deployment
# Copy these values to your Easy Panel environment variables section

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Authentication Configuration
BETTER_AUTH_SECRET=your-production-secret-key-here
BETTER_AUTH_URL=https://sumopod-backend.rl5j77.easypanel.host
BETTER_AUTH_TRUSTED_ORIGINS=https://sumopod-frontend.rl5j77.easypanel.host

# Xendit Payment Gateway Configuration
XENDIT_API_KEY=your-production-xendit-api-key
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=your-production-xendit-callback-token

# Server Configuration
PORT=8080
NODE_ENV=production

# CORS Configuration
CORS_ORIGINS=https://sumopod-frontend.rl5j77.easypanel.host
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# Application Configuration
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# Sentry Error Monitoring
SENTRY_DSN=https://<EMAIL>/4509676288868432
