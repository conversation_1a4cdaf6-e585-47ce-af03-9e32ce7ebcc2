import * as Sentry from "@sentry/bun";
import type { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";

/**
 * Sentry error handler middleware for Hono
 * Captures unhandled errors and sends them to Sentry with proper context
 */
export const sentryErrorHandler = async (c: Context, next: Next) => {
  try {
    await next();
  } catch (error) {
    // Don't report expected HTTP exceptions to Sentry
    if (error instanceof HTTPException) {
      throw error;
    }

    // Add request context to Sentry
    Sentry.withScope((scope) => {
      // Add request information
      scope.setTag("method", c.req.method);
      scope.setTag("url", c.req.url);
      scope.setTag("path", c.req.path);
      
      // Add user context if available
      const userId = c.get("userId");
      if (userId) {
        scope.setUser({ id: userId });
      }

      // Add request headers (excluding sensitive ones)
      const headers = Object.fromEntries(c.req.raw.headers.entries());
      const sanitizedHeaders = { ...headers };
      
      // Remove sensitive headers
      delete sanitizedHeaders.authorization;
      delete sanitizedHeaders.cookie;
      delete sanitizedHeaders["x-session-token"];
      
      scope.setContext("headers", sanitizedHeaders);

      // Add query parameters
      const url = new URL(c.req.url);
      if (url.search) {
        scope.setContext("query", Object.fromEntries(url.searchParams.entries()));
      }

      // Capture the exception
      Sentry.captureException(error);
    });

    // Re-throw the error to let Hono handle the response
    throw error;
  }
};

/**
 * Sentry request tracing middleware
 * Creates a span for each request to track performance
 */
export const sentryTracingMiddleware = async (c: Context, next: Next) => {
  return await Sentry.startSpan({
    op: "http.server",
    name: `${c.req.method} ${c.req.path}`,
    attributes: {
      "http.method": c.req.method,
      "http.url": c.req.url,
      "http.route": c.req.path,
    },
  }, async (span) => {
    try {
      await next();

      // Set span status based on response
      const status = c.res.status;
      span.setAttributes({
        "http.status_code": status,
      });

      if (status >= 400) {
        span.setStatus({ code: 2, message: "Error" }); // ERROR
      } else {
        span.setStatus({ code: 1 }); // OK
      }
    } catch (error) {
      span.setStatus({ code: 2, message: "Internal Error" }); // ERROR
      throw error;
    }
  });
};
