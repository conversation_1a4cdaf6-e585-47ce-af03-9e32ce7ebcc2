import { PrismaClient } from "@prisma/client";

/**
 * Centralized Prisma client instance
 * This ensures we have a single instance across the application
 * and proper configuration for tracing and logging
 */

// Global variable to store the Prisma client instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with proper configuration
const createPrismaClient = () => {
  return new PrismaClient({
    log: ["warn", "error"],
  });
};

// Use global variable in development to prevent multiple instances
// In production, create a new instance
const prisma = globalThis.__prisma ?? createPrismaClient();

if (process.env.NODE_ENV !== "production") {
  globalThis.__prisma = prisma;
}

export { prisma };
export default prisma;
