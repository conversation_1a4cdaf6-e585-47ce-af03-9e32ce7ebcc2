import { betterAuth } from "better-auth";
import { bearer } from "better-auth/plugins";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { prisma } from "./prisma";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
  },
  plugins: [bearer()],
  baseURL: process.env.BETTER_AUTH_URL || "https://sumopod-backend.fly.dev",
  trustedOrigins: process.env.BETTER_AUTH_TRUSTED_ORIGINS?.split(',') || ["https://cloone-sumopod.netlify.app"],
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          await prisma.balance.create({
            data: {
              userId: user.id,
              userBalance: 0,
            },
          });
        },
      },
    },
  },
});

export { prisma };
