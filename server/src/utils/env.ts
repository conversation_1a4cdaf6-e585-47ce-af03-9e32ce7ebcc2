/**
 * Server environment variable validation utility
 * Ensures required environment variables are present at runtime
 */

interface ServerEnvConfig {
  DATABASE_URL: string;
  BETTER_AUTH_SECRET: string;
  BETTER_AUTH_URL: string;
  BETTER_AUTH_TRUSTED_ORIGINS: string;
  XENDIT_API_KEY: string;
  XENDIT_API_URL: string;
  XENDIT_CALLBACK_TOKEN: string;
  PORT: string;
  CORS_ORIGINS: string;
  CORS_ALLOW_HEADERS: string;
  CORS_ALLOW_METHODS: string;
  APP_NAME: string;
  EXTERNAL_ID_PREFIX: string;
  SENTRY_DSN: string;
}

/**
 * Validates and returns server environment variables
 * Throws an error if required variables are missing
 */
export function validateServerEnv(): ServerEnvConfig {
  const requiredVars = {
    DATABASE_URL: process.env.DATABASE_URL,
    BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET,
    BETTER_AUTH_URL: process.env.BETTER_AUTH_URL,
    BETTER_AUTH_TRUSTED_ORIGINS: process.env.BETTER_AUTH_TRUSTED_ORIGINS,
    XENDIT_API_KEY: process.env.XENDIT_API_KEY,
    XENDIT_API_URL: process.env.XENDIT_API_URL || 'https://api.xendit.co/v2/invoices',
    XENDIT_CALLBACK_TOKEN: process.env.XENDIT_CALLBACK_TOKEN,
    PORT: process.env.PORT || '8080',
    CORS_ORIGINS: process.env.CORS_ORIGINS || 'http://localhost:3001,https://cloone-sumopod.netlify.app',
    CORS_ALLOW_HEADERS: process.env.CORS_ALLOW_HEADERS || 'Content-Type,Authorization,X-Session-Token',
    CORS_ALLOW_METHODS: process.env.CORS_ALLOW_METHODS || 'GET,POST,PUT,DELETE,OPTIONS',
    APP_NAME: process.env.APP_NAME || 'sumopod-backend',
    EXTERNAL_ID_PREFIX: process.env.EXTERNAL_ID_PREFIX || 'sumopod-',
    SENTRY_DSN: process.env.SENTRY_DSN,
  };

  const missingVars: string[] = [];

  // Check for missing required variables (those without defaults)
  const requiredWithoutDefaults = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL',
    'BETTER_AUTH_TRUSTED_ORIGINS',
    'XENDIT_API_KEY',
    'XENDIT_CALLBACK_TOKEN',
    'SENTRY_DSN',
  ];

  requiredWithoutDefaults.forEach((key) => {
    const value = requiredVars[key as keyof ServerEnvConfig];
    if (!value || value.trim() === '') {
      missingVars.push(key);
    }
  });

  if (missingVars.length > 0) {
    const errorMessage = `Missing required environment variables: ${missingVars.join(', ')}. Please check your .env file.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  return requiredVars as ServerEnvConfig;
}

/**
 * Get validated server environment configuration
 * Use this instead of directly accessing process.env
 */
export const serverEnv = validateServerEnv();

/**
 * Safe environment variable getters with validation
 */
export function getDatabaseUrl(): string {
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    throw new Error('DATABASE_URL is not configured. Please check your .env file.');
  }
  return dbUrl;
}

export function getXenditApiKey(): string {
  const apiKey = process.env.XENDIT_API_KEY;
  if (!apiKey) {
    throw new Error('XENDIT_API_KEY is not configured. Please check your .env file.');
  }
  return apiKey;
}

export function getXenditApiUrl(): string {
  return process.env.XENDIT_API_URL || 'https://api.xendit.co/v2/invoices';
}

export function getXenditCallbackToken(): string {
  const token = process.env.XENDIT_CALLBACK_TOKEN;
  if (!token) {
    throw new Error('XENDIT_CALLBACK_TOKEN is not configured. Please check your .env file.');
  }
  return token;
}

export function getExternalIdPrefix(): string {
  return process.env.EXTERNAL_ID_PREFIX || 'sumopod-';
}

export function getPort(): number {
  return parseInt(process.env.PORT || '8080', 10);
}

export function getCorsOrigins(): string[] {
  const origins = process.env.CORS_ORIGINS || 'http://localhost:3001,https://cloone-sumopod.netlify.app';
  return origins.split(',').map(origin => origin.trim());
}

export function getCorsAllowHeaders(): string[] {
  const headers = process.env.CORS_ALLOW_HEADERS || 'Content-Type,Authorization,X-Session-Token';
  return headers.split(',').map(header => header.trim());
}

export function getCorsAllowMethods(): string[] {
  const methods = process.env.CORS_ALLOW_METHODS || 'GET,POST,PUT,DELETE,OPTIONS';
  return methods.split(',').map(method => method.trim());
}

export function getSentryDsn(): string {
  const dsn = process.env.SENTRY_DSN;
  if (!dsn) {
    throw new Error('SENTRY_DSN is not configured. Please check your .env file.');
  }
  return dsn;
}
