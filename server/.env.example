# Database
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Authentication
BETTER_AUTH_SECRET=your-secret-key-here
BETTER_AUTH_URL=https://your-backend-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-frontend-domain.com

# Xendit Payment Gateway
XENDIT_API_KEY=your-xendit-api-key
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=your-xendit-callback-token

# Server Configuration
PORT=8080
NODE_ENV=production

# CORS Configuration
CORS_ORIGINS=https://your-frontend-domain.com
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# Application Configuration
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# Sentry Error Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
