# Sentry Frontend Integration

## Overview

Sentry is integrated into the React frontend for comprehensive error monitoring and performance tracking. The integration uses **runtime environment variables** to ensure secure DSN handling without exposing secrets during build time.

## Architecture

### Runtime Environment System
- **env.template.js**: Template file with placeholder variables
- **docker-entrypoint**: Validates and injects environment variables at container startup
- **config.ts**: Runtime configuration reader that accesses `window._env_`

### Key Features
- ✅ **Runtime DSN injection** - No secrets in build artifacts
- ✅ **Error Boundary** - Catches React component errors
- ✅ **Session Replay** - Records user sessions for debugging
- ✅ **Performance Monitoring** - Tracks application performance
- ✅ **Automatic Error Reporting** - Unhandled errors sent to Sentry
- ✅ **Development Testing** - Test route for error verification

## Configuration

### Environment Variables

Required environment variable:
```bash
SENTRY_DSN=https://<EMAIL>/project-id
```

### Files Modified

1. **public/env.template.js** - Added SENTRY_DSN placeholder
2. **docker-entrypoint** - Added SENTRY_DSN validation
3. **src/config.ts** - Added getSentryDsn() function
4. **src/index.tsx** - Sentry initialization and Error Boundary
5. **.env** and **.env.easypanel** - Added SENTRY_DSN values

## Features

### Error Boundary
Wraps the entire React app to catch component lifecycle errors:
```jsx
<Sentry.ErrorBoundary fallback={ErrorFallback}>
  <App />
</Sentry.ErrorBoundary>
```

### Session Replay
Records user interactions for debugging:
- Production: 10% of sessions
- Development: 100% of sessions
- Error sessions: 100% recorded

### Performance Monitoring
Tracks application performance:
- Production: 10% sample rate
- Development: 100% sample rate

## Testing

### Manual Error Testing (Development Only)
```javascript
// Throw an error to test Sentry in development
throw new Error("Test error for Sentry");

// Or capture manually
Sentry.captureException(new Error("Manual test error"));
```

## Deployment

### Docker Build
The Dockerfile handles runtime environment injection:
1. Copies `env.template.js` to container
2. `docker-entrypoint` validates required env vars
3. `envsubst` generates `env.js` from template
4. Template file is removed for security

### Environment Setup
Set these environment variables in your deployment platform:
```bash
SENTRY_DSN=https://<EMAIL>/project-id
```

## Security

### No Build-Time Secrets
- DSN is injected at runtime, not build time
- No secrets exposed in build artifacts
- Template file removed after env generation

### Error Filtering
Sentry automatically filters sensitive data, but additional filtering can be added in the `beforeSend` callback if needed.

## Monitoring

### What Gets Reported
- ✅ Unhandled JavaScript errors
- ✅ React component errors
- ✅ Promise rejections
- ✅ Network request failures
- ✅ Performance metrics
- ✅ User sessions (with replay)

### What Doesn't Get Reported
- ❌ Handled errors (unless manually captured)
- ❌ Console logs
- ❌ Expected validation errors
- ❌ HTTP 4xx responses (unless they cause JS errors)

## Best Practices

1. **Error Boundaries**: Use Sentry.ErrorBoundary for component error catching
2. **Manual Capture**: Use `Sentry.captureException()` for handled errors you want to track
3. **User Context**: Add user information for better debugging
4. **Performance**: Monitor Core Web Vitals and custom metrics
5. **Privacy**: Be mindful of PII in error reports

## Troubleshooting

### Common Issues

1. **"SENTRY_DSN environment variable is required"**
   - Ensure SENTRY_DSN is set in deployment environment
   - Check docker-entrypoint validation

2. **Sentry not initializing**
   - Check browser console for initialization errors
   - Verify DSN format and validity

3. **No errors appearing in Sentry**
   - Test with `/sentry-test` route in development
   - Check network tab for Sentry API calls
   - Verify project DSN and environment

### Debug Mode
Add to Sentry.init() for debugging:
```javascript
Sentry.init({
  debug: true, // Enable debug mode
  // ... other options
});
```

## Integration Complete ✅

Sentry frontend integration is production-ready with:
- Runtime environment variable configuration
- Comprehensive error catching
- Session replay for debugging
- Performance monitoring
- Secure deployment without secret exposure
