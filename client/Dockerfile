# ===== SHARED LIBRARY BUILD STAGE =====
FROM node:20-alpine AS shared-builder

WORKDIR /app

# Install build dependencies with virtual package for easy cleanup
RUN apk add --no-cache --virtual .build-deps python3 make g++ \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc

# Copy shared package.json and install dependencies
COPY shared/package.json shared/tsconfig.json ./shared/
WORKDIR /app/shared
RUN npm install --no-audit --no-fund --silent \
    && npm cache clean --force \
    && rm -rf ~/.npm /tmp/*

# Copy shared source and build with aggressive cleanup
COPY shared/src ./src
RUN npm run build \
    && rm -rf node_modules src *.config.* tsconfig.json \
    && find . -name "*.map" -delete \
    && find . -name "*.env*" -type f -delete \
    && find . -name "*.test.*" -type f -delete \
    && find . -name "*.spec.*" -type f -delete \
    && find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true

# ===== CLIENT BUILD STAGE =====
FROM node:20-alpine AS client-builder

WORKDIR /app

# Install build dependencies in single layer with cleanup
RUN apk add --no-cache python3 make g++ \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/*

# Copy root tsconfig.json and client files
COPY tsconfig.json ./tsconfig.json
COPY client/package.json client/tsconfig*.json client/vite.config.ts client/index.html ./client/
WORKDIR /app/client

# Replace workspace protocol with file protocol for Docker build
RUN sed -i 's/"shared": "workspace:\*"/"shared": "file:..\/shared"/g' package.json \
    && npm install --no-audit --no-fund \
    && npm cache clean --force

# Copy built shared library from previous stage
COPY --from=shared-builder /app/shared/dist /app/shared/dist
COPY --from=shared-builder /app/shared/package.json /app/shared/package.json

# Copy client source and build
COPY client/src ./src
COPY client/public ./public
RUN npm run build \
    && rm -rf node_modules src public *.config.* *.json README.md \
    && find . -name "*.map" -delete \
    && find . -name "*.env*" -type f -delete \
    && find . -name "*.test.*" -type f -delete \
    && find . -name "*.spec.*" -type f -delete \
    && find . -name "*.md" -type f -delete \
    && find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true

# ===== PRODUCTION STAGE =====
# Use minimal nginx alpine for smallest runtime
FROM nginx:1.25-alpine AS production

# Create non-root user and install minimal runtime deps with virtual package
RUN apk add --no-cache --virtual .runtime-deps gettext dumb-init \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc /root/.cache \
    && addgroup -g 1001 -S nginx-app \
    && adduser -S appuser -u 1001 -G nginx-app

# Copy only essential built files (minimal layer)
COPY --from=client-builder --chown=appuser:nginx-app /app/client/dist /usr/share/nginx/html

# Copy configuration files in single layer
COPY --chown=appuser:nginx-app client/nginx.conf /etc/nginx/nginx.conf
COPY --chown=appuser:nginx-app client/public/env.template.js /usr/share/nginx/html/env.template.js
COPY --chown=appuser:nginx-app client/docker-entrypoint /usr/local/bin/docker-entrypoint

# Configure nginx, compress assets, and set permissions in single optimized layer
RUN rm -f /etc/nginx/conf.d/default.conf \
    && rm -rf /var/log/nginx/* /var/cache/nginx/* \
    && mkdir -p /var/log/nginx /var/cache/nginx \
    && chmod +x /usr/local/bin/docker-entrypoint \
    && chown -R appuser:nginx-app /usr/share/nginx/html /var/log/nginx /var/cache/nginx \
    && chmod -R 755 /usr/share/nginx/html \
    && find /usr/share/nginx/html -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" -o -name "*.json" \) -exec gzip -9 -k {} \; \
    && find /usr/share/nginx/html -type f -name "*.svg" -exec gzip -9 -k {} \; \
    && find /usr/share/nginx/html -type f -name "*.txt" -exec gzip -9 -k {} \;

# Security: Use non-root user
USER appuser

# Optimized health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider --timeout=2 http://localhost/ || exit 1

# Expose port
EXPOSE 80

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["docker-entrypoint"]
