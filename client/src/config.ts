// Runtime configuration that reads from window._env_ (production) or import.meta.env (development)
// This allows environment variables to be set at runtime in production, with fallback for development

declare global {
  interface Window {
    _env_?: {
      API_URL?: string;
      APP_NAME?: string;
      GOLD_API?: string;
      SENTRY_DSN?: string;
    };
  }
}

// Helper function to get environment variable with fallback
function getEnvVar(key: string, required: boolean = true): string {
  // Try runtime env first (production)
  const runtimeValue = window._env_?.[key as keyof typeof window._env_];
  if (runtimeValue) {
    return runtimeValue;
  }

  // Fallback to Vite env (development)
  const viteValue = import.meta.env[`VITE_${key}`];
  if (viteValue) {
    return viteValue;
  }

  if (required) {
    throw new Error(`${key} environment variable is required but not set`);
  }

  return '';
}

// Configuration with runtime environment variables and development fallback
const config = {
  // API base URL - reads from runtime env with development fallback
  get apiBaseUrl(): string {
    return getEnvVar('API_URL');
  },

  // Gold API URL - reads from runtime env with development fallback
  get goldApiUrl(): string {
    return getEnvVar('GOLD_API');
  },

  // Application name - reads from runtime env with development fallback
  get appName(): string {
    return getEnvVar('APP_NAME');
  },

  // Sentry DSN - reads from runtime env with development fallback
  get sentryDsn(): string {
    return getEnvVar('SENTRY_DSN');
  },
};

// Helper functions for accessing configuration values
export function getApiBaseUrl(): string {
  return config.apiBaseUrl;
}

export function getGoldApiUrl(): string {
  return config.goldApiUrl;
}

export function getAppName(): string {
  return config.appName;
}

export function getSentryDsn(): string {
  return config.sentryDsn;
}

export default config;
