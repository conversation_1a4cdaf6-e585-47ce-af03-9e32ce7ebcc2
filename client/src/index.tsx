import React from "react";
import ReactDOM from "react-dom/client";
import * as Sentry from "@sentry/react";
import "./index.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { getSentryDsn } from "./config";

// Initialize Sentry as early as possible
try {
  Sentry.init({
    dsn: getSentryDsn(),
    environment: process.env.NODE_ENV || 'development',
    // Setting this option to true will send default PII data to Sentry
    sendDefaultPii: true,
    // Performance monitoring
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    // Session replay for debugging
    replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,
    integrations: [
      Sentry.replayIntegration(),
      // Send console.error and console.warn as logs to Sentry
      Sentry.consoleLoggingIntegration({ levels: ["error", "warn"] }),
    ],
    // Enable experimental logging features
    _experiments: {
      enableLogs: true,
    },
  });
} catch (error) {
  console.warn('Failed to initialize Sentry:', error);
}

const root = ReactDOM.createRoot(
	document.getElementById("root") as HTMLElement,
);
root.render(
	<React.StrictMode>
		<Sentry.ErrorBoundary fallback={({ error, resetError }) => (
			<div style={{ padding: '20px', textAlign: 'center' }}>
				<h2>Something went wrong</h2>
				<p>An error occurred and has been reported to our team.</p>
				<details style={{ marginTop: '10px' }}>
					<summary>Error details</summary>
					<pre style={{ textAlign: 'left', background: '#f5f5f5', padding: '10px', marginTop: '10px' }}>
						{error instanceof Error ? error.message : String(error)}
					</pre>
				</details>
				<button type="button" onClick={resetError} style={{ marginTop: '10px', padding: '8px 16px' }}>
					Try again
				</button>
			</div>
		)}>
			<App />
		</Sentry.ErrorBoundary>
	</React.StrictMode>,
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
