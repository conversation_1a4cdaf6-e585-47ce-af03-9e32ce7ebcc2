#!/bin/sh -e

# Validate required environment variables
if [ -z "$API_URL" ]; then
  echo "Error: API_URL environment variable is required but not set"
  exit 1
fi

if [ -z "$APP_NAME" ]; then
  echo "Error: APP_NAME environment variable is required but not set"
  exit 1
fi

if [ -z "$GOLD_API" ]; then
  echo "Error: GOLD_API environment variable is required but not set"
  exit 1
fi

if [ -z "$SENTRY_DSN" ]; then
  echo "Error: SENTRY_DSN environment variable is required but not set"
  exit 1
fi

# Generate env.js from template using envsubst
envsubst < /usr/share/nginx/html/env.template.js > /usr/share/nginx/html/env.js

# Remove the template file for security
rm -f /usr/share/nginx/html/env.template.js

# Start nginx
exec "${@}"
